# Kritrima AI CLI - WSL Sync Setup Script
# Quick setup script to prepare the WSL sync environment

param(
    [switch]$Force,
    [switch]$Help
)

# Colors for output
$Colors = @{
    Success = "Green"
    Error = "Red"
    Warning = "Yellow"
    Info = "Cyan"
    Header = "Magenta"
}

# Emojis
$Emojis = @{
    Rocket = "🚀"
    Check = "✅"
    Cross = "❌"
    Warning = "⚠️"
    Info = "ℹ️"
    Gear = "⚙️"
    Robot = "🤖"
}

function Show-Header {
    Clear-Host
    Write-Host ""
    Write-Host "$($Emojis.Robot) Kritrima AI CLI - WSL Sync Setup" -ForegroundColor $Colors.Header
    Write-Host "Quick setup for Windows to WSL synchronization" -ForegroundColor $Colors.Header
    Write-Host "=============================================" -ForegroundColor $Colors.Header
    Write-Host ""
}

function Show-Help {
    Show-Header
    Write-Host "USAGE:" -ForegroundColor $Colors.Info
    Write-Host "  .\setup-wsl-sync.ps1 [OPTIONS]" -ForegroundColor White
    Write-Host ""
    Write-Host "OPTIONS:" -ForegroundColor $Colors.Info
    Write-Host "  -Force    Overwrite existing files without prompting" -ForegroundColor White
    Write-Host "  -Help     Show this help message" -ForegroundColor White
    Write-Host ""
    Write-Host "DESCRIPTION:" -ForegroundColor $Colors.Info
    Write-Host "  This script prepares your environment for WSL synchronization by:" -ForegroundColor White
    Write-Host "  • Checking PowerShell execution policy" -ForegroundColor White
    Write-Host "  • Verifying required files are present" -ForegroundColor White
    Write-Host "  • Creating configuration files" -ForegroundColor White
    Write-Host "  • Setting up shortcuts and aliases" -ForegroundColor White
    Write-Host ""
}

function Test-ExecutionPolicy {
    Write-Host "$($Emojis.Info) Checking PowerShell execution policy..." -ForegroundColor $Colors.Info
    
    $policy = Get-ExecutionPolicy -Scope CurrentUser
    
    if ($policy -eq "Restricted") {
        Write-Host "$($Emojis.Warning) PowerShell execution policy is Restricted" -ForegroundColor $Colors.Warning
        Write-Host "This will prevent the sync script from running." -ForegroundColor $Colors.Warning
        Write-Host ""
        
        $response = Read-Host "Would you like to set the execution policy to RemoteSigned for the current user? (Y/n)"
        if ($response -ne 'n' -and $response -ne 'N') {
            try {
                Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser -Force
                Write-Host "$($Emojis.Check) Execution policy updated to RemoteSigned" -ForegroundColor $Colors.Success
                return $true
            }
            catch {
                Write-Host "$($Emojis.Cross) Failed to update execution policy: $($_.Exception.Message)" -ForegroundColor $Colors.Error
                return $false
            }
        }
        else {
            Write-Host "$($Emojis.Warning) Execution policy not changed. You may need to run scripts with -ExecutionPolicy Bypass" -ForegroundColor $Colors.Warning
            return $false
        }
    }
    else {
        Write-Host "$($Emojis.Check) PowerShell execution policy is $policy" -ForegroundColor $Colors.Success
        return $true
    }
}

function Test-RequiredFiles {
    Write-Host "$($Emojis.Info) Checking required files..." -ForegroundColor $Colors.Info
    
    $requiredFiles = @(
        "sync-to-wsl.ps1",
        "sync-to-wsl.bat",
        "WSL-SYNC-README.md"
    )
    
    $missingFiles = @()
    foreach ($file in $requiredFiles) {
        if (-not (Test-Path $file)) {
            $missingFiles += $file
        }
    }
    
    if ($missingFiles.Count -gt 0) {
        Write-Host "$($Emojis.Cross) Missing required files:" -ForegroundColor $Colors.Error
        foreach ($file in $missingFiles) {
            Write-Host "  • $file" -ForegroundColor $Colors.Error
        }
        return $false
    }
    else {
        Write-Host "$($Emojis.Check) All required files are present" -ForegroundColor $Colors.Success
        return $true
    }
}

function New-ConfigurationFile {
    Write-Host "$($Emojis.Info) Setting up configuration..." -ForegroundColor $Colors.Info
    
    $configFile = "sync-config.ps1"
    $exampleFile = "sync-config.example.ps1"
    
    if (Test-Path $configFile) {
        if (-not $Force) {
            $response = Read-Host "Configuration file already exists. Overwrite? (y/N)"
            if ($response -ne 'y' -and $response -ne 'Y') {
                Write-Host "$($Emojis.Info) Keeping existing configuration file" -ForegroundColor $Colors.Info
                return $true
            }
        }
    }
    
    if (Test-Path $exampleFile) {
        try {
            Copy-Item $exampleFile $configFile -Force
            Write-Host "$($Emojis.Check) Configuration file created: $configFile" -ForegroundColor $Colors.Success
            Write-Host "You can customize it by editing $configFile" -ForegroundColor $Colors.Info
            return $true
        }
        catch {
            Write-Host "$($Emojis.Cross) Failed to create configuration file: $($_.Exception.Message)" -ForegroundColor $Colors.Error
            return $false
        }
    }
    else {
        Write-Host "$($Emojis.Warning) Example configuration file not found. Skipping..." -ForegroundColor $Colors.Warning
        return $true
    }
}

function New-DesktopShortcuts {
    Write-Host "$($Emojis.Info) Creating desktop shortcuts..." -ForegroundColor $Colors.Info
    
    $response = Read-Host "Create desktop shortcuts for easy access? (Y/n)"
    if ($response -eq 'n' -or $response -eq 'N') {
        Write-Host "$($Emojis.Info) Skipping desktop shortcuts" -ForegroundColor $Colors.Info
        return $true
    }
    
    try {
        $desktop = [Environment]::GetFolderPath("Desktop")
        $scriptPath = Join-Path $PSScriptRoot "sync-to-wsl.bat"
        
        # Create shortcut for initial setup
        $shortcutPath = Join-Path $desktop "Kritrima WSL Setup.lnk"
        $WshShell = New-Object -comObject WScript.Shell
        $Shortcut = $WshShell.CreateShortcut($shortcutPath)
        $Shortcut.TargetPath = $scriptPath
        $Shortcut.WorkingDirectory = $PSScriptRoot
        $Shortcut.Description = "Kritrima AI CLI - WSL Initial Setup"
        $Shortcut.Save()
        
        Write-Host "$($Emojis.Check) Desktop shortcut created: Kritrima WSL Setup.lnk" -ForegroundColor $Colors.Success
        return $true
    }
    catch {
        Write-Host "$($Emojis.Warning) Failed to create desktop shortcuts: $($_.Exception.Message)" -ForegroundColor $Colors.Warning
        return $false
    }
}

function Show-NextSteps {
    Write-Host ""
    Write-Host "$($Emojis.Rocket) Setup Complete!" -ForegroundColor $Colors.Success
    Write-Host "==================" -ForegroundColor $Colors.Success
    Write-Host ""
    Write-Host "Next Steps:" -ForegroundColor $Colors.Info
    Write-Host ""
    Write-Host "1. Review and customize configuration (optional):" -ForegroundColor White
    Write-Host "   notepad sync-config.ps1" -ForegroundColor $Colors.Info
    Write-Host ""
    Write-Host "2. Run initial setup:" -ForegroundColor White
    Write-Host "   .\sync-to-wsl.ps1 -InitialSetup" -ForegroundColor $Colors.Info
    Write-Host "   OR double-click: sync-to-wsl.bat" -ForegroundColor $Colors.Info
    Write-Host ""
    Write-Host "3. Start development with auto-sync:" -ForegroundColor White
    Write-Host "   .\sync-to-wsl.ps1 -WatchMode" -ForegroundColor $Colors.Info
    Write-Host ""
    Write-Host "4. Read the documentation:" -ForegroundColor White
    Write-Host "   notepad WSL-SYNC-README.md" -ForegroundColor $Colors.Info
    Write-Host ""
    Write-Host "Quick Commands:" -ForegroundColor $Colors.Info
    Write-Host "  .\sync-to-wsl.ps1 -Help          # Show help" -ForegroundColor $Colors.Info
    Write-Host "  .\sync-to-wsl.ps1 -InitialSetup  # First-time setup" -ForegroundColor $Colors.Info
    Write-Host "  .\sync-to-wsl.ps1 -WatchMode     # Start file watching" -ForegroundColor $Colors.Info
    Write-Host "  .\sync-to-wsl.ps1 -BuildOnly     # Just build project" -ForegroundColor $Colors.Info
    Write-Host ""
}

# ============================================================================
# MAIN EXECUTION
# ============================================================================

if ($Help) {
    Show-Help
    exit 0
}

Show-Header

Write-Host "This script will prepare your environment for WSL synchronization." -ForegroundColor $Colors.Info
Write-Host ""

# Run setup steps
$success = $true

if (-not (Test-ExecutionPolicy)) {
    $success = $false
}

if (-not (Test-RequiredFiles)) {
    $success = $false
}

if ($success) {
    New-ConfigurationFile | Out-Null
    New-DesktopShortcuts | Out-Null
    Show-NextSteps
}
else {
    Write-Host ""
    Write-Host "$($Emojis.Cross) Setup failed. Please resolve the issues above and try again." -ForegroundColor $Colors.Error
    Write-Host ""
    exit 1
}

Write-Host ""
Write-Host "$($Emojis.Check) Happy coding with seamless WSL integration! $($Emojis.Robot)" -ForegroundColor $Colors.Success
Write-Host ""
