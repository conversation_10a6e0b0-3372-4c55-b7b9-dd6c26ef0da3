# 🚀 Kritrima AI CLI - WSL Auto-Sync Script

**Production-ready automatic project synchronization and build system for Windows WSL**

This PowerShell script provides seamless integration between your Windows development environment and WSL (Windows Subsystem for Linux), automatically syncing your Kritrima AI CLI project files, installing dependencies, building the project, and watching for changes.

## ✨ Features

### 🔄 **Automatic Synchronization**
- **Initial Setup**: Complete project sync from Windows to WSL
- **Incremental Updates**: Only syncs changed files for efficiency
- **Real-time Watching**: Monitors file changes and auto-syncs
- **Smart Filtering**: Excludes unnecessary files (node_modules, .git, etc.)

### 🛠️ **Build Automation**
- **Dependency Management**: Automatic npm install in WSL
- **Build Pipeline**: Runs linting, formatting, and build commands
- **Test Execution**: Optional test running with results
- **Global Installation**: Option to install CLI globally in WSL

### 🎯 **Intelligent Features**
- **Prerequisites Check**: Validates WSL, Node.js, and distribution
- **Error Handling**: Robust error recovery and logging
- **Progress Tracking**: Real-time status updates with emojis
- **Configuration**: Flexible settings for different environments

### 🔒 **Safety & Reliability**
- **File Hash Verification**: Detects actual file changes
- **Graceful Interruption**: Handles Ctrl+C cleanly
- **Comprehensive Logging**: Detailed logs for troubleshooting
- **Rollback Support**: Safe operations with error recovery

## 🚀 Quick Start

### Prerequisites

1. **Windows 11** with WSL enabled
2. **WSL Distribution** (Ubuntu recommended)
3. **PowerShell 5.1+** (Windows PowerShell or PowerShell Core)
4. **Node.js 18+** will be installed automatically in WSL

### Installation

1. **Download the script** to your project directory:
   ```powershell
   # The script should be in the same directory as your package.json
   ```

2. **Set execution policy** (if needed):
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   ```

### First-Time Setup

Run the initial setup to sync your project and install everything:

```powershell
.\sync-to-wsl.ps1 -InitialSetup
```

This will:
- ✅ Check WSL availability and distribution
- ✅ Install Node.js 18+ in WSL (if needed)
- ✅ Sync all project files to WSL
- ✅ Install npm dependencies
- ✅ Run build pipeline (lint, format, build)
- ✅ Run tests (optional)
- ✅ Install CLI globally (optional)

## 📖 Usage Guide

### Command Line Options

```powershell
.\sync-to-wsl.ps1 [OPTIONS]

OPTIONS:
  -InitialSetup       Perform initial project setup in WSL
  -WatchMode          Start file watching mode for auto-sync
  -BuildOnly          Only run build commands (no sync)
  -WSLDistro          WSL distribution name (default: Ubuntu)
  -WSLProjectPath     Project path in WSL (default: /home/<USER>/kritrima-ai-cli)
  -Verbose            Enable verbose logging
  -Help               Show help message
```

### Common Usage Patterns

#### 1. Initial Project Setup
```powershell
# First time setup with default settings
.\sync-to-wsl.ps1 -InitialSetup

# Setup with custom WSL distribution
.\sync-to-wsl.ps1 -InitialSetup -WSLDistro "Ubuntu-22.04"

# Setup with custom WSL path
.\sync-to-wsl.ps1 -InitialSetup -WSLProjectPath "/home/<USER>/my-project"
```

#### 2. File Watching Mode
```powershell
# Start watching for changes (after initial setup)
.\sync-to-wsl.ps1 -WatchMode

# Watch with verbose logging
.\sync-to-wsl.ps1 -WatchMode -Verbose
```

#### 3. Build Only Mode
```powershell
# Just run build commands without syncing
.\sync-to-wsl.ps1 -BuildOnly

# Useful for CI/CD or when files are already synced
```

#### 4. Interactive Mode
```powershell
# Run without parameters for interactive menu
.\sync-to-wsl.ps1
```

### File Watching

The script monitors these file types for changes:
- `*.js` - JavaScript files
- `*.json` - Configuration files
- `*.md` - Documentation
- `*.yml`, `*.yaml` - YAML configuration
- `*.sh`, `*.ps1` - Scripts

**Excluded from sync:**
- `node_modules/` - Dependencies
- `.git/` - Git repository
- `*.log` - Log files
- `.env` - Environment files
- Temporary files

## 🔧 Configuration

### Default Settings

```powershell
$CONFIG = @{
    WSLDistro = "Ubuntu"                                    # WSL distribution
    WSLProjectPath = "/home/<USER>/kritrima-ai-cli"  # Project path in WSL
    WindowsProjectPath = $PSScriptRoot                     # Current directory
    NodeVersion = "18"                                      # Required Node.js version
    ExcludePatterns = @("node_modules", ".git", "*.log")   # Files to exclude
    WatchPatterns = @("*.js", "*.json", "*.md")           # Files to watch
}
```

### Customization

You can modify the script or pass parameters to customize:

```powershell
# Use different WSL distribution
.\sync-to-wsl.ps1 -InitialSetup -WSLDistro "Debian"

# Use different project path
.\sync-to-wsl.ps1 -InitialSetup -WSLProjectPath "/opt/kritrima-cli"

# Enable verbose logging
.\sync-to-wsl.ps1 -WatchMode -Verbose
```

## 🛠️ Development Workflow

### Recommended Workflow

1. **Initial Setup** (once):
   ```powershell
   .\sync-to-wsl.ps1 -InitialSetup
   ```

2. **Start Development** (daily):
   ```powershell
   .\sync-to-wsl.ps1 -WatchMode
   ```

3. **Edit files in Windows** using your favorite editor (VS Code, etc.)

4. **Files automatically sync** to WSL and trigger rebuilds

5. **Test in WSL**:
   ```bash
   wsl -d Ubuntu -- bash -c 'cd /home/<USER>/kritrima-ai-cli && node src/index.js --version'
   ```

### Integration with VS Code

Add these tasks to your `.vscode/tasks.json`:

```json
{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "WSL: Initial Setup",
            "type": "shell",
            "command": "powershell",
            "args": ["-File", "./sync-to-wsl.ps1", "-InitialSetup"],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "new"
            }
        },
        {
            "label": "WSL: Start Watching",
            "type": "shell",
            "command": "powershell",
            "args": ["-File", "./sync-to-wsl.ps1", "-WatchMode"],
            "group": "build",
            "isBackground": true,
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "new"
            }
        }
    ]
}
```

## 🔍 Troubleshooting

### Common Issues

#### WSL Not Found
```
❌ WSL is not available or not installed
```
**Solution**: Install WSL using `wsl --install` in an elevated PowerShell

#### Node.js Installation Fails
```
❌ Failed to install Node.js
```
**Solution**: 
- Check internet connection
- Try manual installation: `wsl -d Ubuntu -- sudo apt update && sudo apt install nodejs npm`

#### Permission Denied
```
❌ Permission denied
```
**Solution**: 
- Run PowerShell as Administrator for initial setup
- Check WSL file permissions

#### Files Not Syncing
```
⚠️ Files not syncing properly
```
**Solution**:
- Check file paths are correct
- Verify files match watch patterns
- Run with `-Verbose` flag for detailed logs

### Log Files

Check the log file for detailed information:
```powershell
Get-Content .\sync-log.txt -Tail 50
```

### Manual Commands

Test individual components:

```powershell
# Test WSL connectivity
wsl -d Ubuntu -- echo "WSL is working"

# Test Node.js in WSL
wsl -d Ubuntu -- node --version

# Test project in WSL
wsl -d Ubuntu -- bash -c 'cd /home/<USER>/kritrima-ai-cli && npm test'
```

## 🤝 Contributing

Feel free to improve this script! Common enhancements:

- Support for additional file types
- Integration with other build tools
- Performance optimizations
- Additional WSL distributions

## 📄 License

This script is part of the Kritrima AI CLI project and follows the same MIT license.

---

**Made with ❤️ for seamless Windows-WSL development**
