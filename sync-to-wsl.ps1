# Kritrima AI CLI - Windows to WSL Auto-Sync Script
# Production-ready automatic project synchronization and build system
# Author: Kritrima AI CLI Team
# Version: 1.0.0

param(
    [string]$WSLDistro = "Ubuntu",
    [string]$WSLProjectPath = "/home/<USER>/kritrima-ai-cli",
    [string]$WindowsProjectPath = $PSScriptRoot,
    [switch]$InitialSetup,
    [switch]$WatchMode,
    [switch]$BuildOnly,
    [switch]$Verbose,
    [switch]$Help
)

# Configuration
$CONFIG = @{
    WSLDistro = $WSLDistro
    WSLProjectPath = $WSLProjectPath
    WindowsProjectPath = $WindowsProjectPath
    LogFile = Join-Path $PSScriptRoot "sync-log.txt"
    ExcludePatterns = @(
        "node_modules",
        ".git",
        "*.log",
        ".env",
        "sync-log.txt",
        "*.tmp",
        ".DS_Store",
        "Thumbs.db"
    )
    WatchPatterns = @(
        "*.js",
        "*.json",
        "*.md",
        "*.yml",
        "*.yaml",
        "*.sh",
        "*.ps1"
    )
    NodeVersion = "18"
}

# Colors for output
$Colors = @{
    Success = "Green"
    Error = "Red"
    Warning = "Yellow"
    Info = "Cyan"
    Debug = "Gray"
    Header = "Magenta"
}

# Emojis for better UX
$Emojis = @{
    Rocket = "🚀"
    Check = "✅"
    Cross = "❌"
    Warning = "⚠️"
    Info = "ℹ️"
    Gear = "⚙️"
    Robot = "🤖"
    Sync = "🔄"
    Build = "🔨"
    Watch = "👀"
}

function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO",
        [string]$Color = "White"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    # Write to console with color
    Write-Host $logMessage -ForegroundColor $Color
    
    # Write to log file
    Add-Content -Path $CONFIG.LogFile -Value $logMessage -ErrorAction SilentlyContinue
}

function Show-Header {
    Clear-Host
    Write-Host ""
    Write-Host "$($Emojis.Robot) Kritrima AI CLI - WSL Auto-Sync" -ForegroundColor $Colors.Header
    Write-Host "Production-ready automatic project synchronization" -ForegroundColor $Colors.Header
    Write-Host "=================================================" -ForegroundColor $Colors.Header
    Write-Host ""
}

function Show-Help {
    Show-Header
    Write-Host "USAGE:" -ForegroundColor $Colors.Info
    Write-Host "  .\sync-to-wsl.ps1 [OPTIONS]" -ForegroundColor White
    Write-Host ""
    Write-Host "OPTIONS:" -ForegroundColor $Colors.Info
    Write-Host "  -InitialSetup    Perform initial project setup in WSL" -ForegroundColor White
    Write-Host "  -WatchMode       Start file watching mode for auto-sync" -ForegroundColor White
    Write-Host "  -BuildOnly       Only run build commands (no sync)" -ForegroundColor White
    Write-Host "  -WSLDistro       WSL distribution name (default: Ubuntu)" -ForegroundColor White
    Write-Host "  -WSLProjectPath  Project path in WSL (default: /home/<USER>/kritrima-ai-cli)" -ForegroundColor White
    Write-Host "  -Verbose         Enable verbose logging" -ForegroundColor White
    Write-Host "  -Help            Show this help message" -ForegroundColor White
    Write-Host ""
    Write-Host "EXAMPLES:" -ForegroundColor $Colors.Info
    Write-Host "  .\sync-to-wsl.ps1 -InitialSetup" -ForegroundColor White
    Write-Host "  .\sync-to-wsl.ps1 -WatchMode" -ForegroundColor White
    Write-Host "  .\sync-to-wsl.ps1 -BuildOnly" -ForegroundColor White
    Write-Host ""
}

function Test-WSLAvailability {
    Write-Log "$($Emojis.Info) Checking WSL availability..." "INFO" $Colors.Info
    
    try {
        $wslVersion = wsl --version 2>$null
        if ($LASTEXITCODE -ne 0) {
            throw "WSL command failed"
        }
        Write-Log "$($Emojis.Check) WSL is available" "SUCCESS" $Colors.Success
        return $true
    }
    catch {
        Write-Log "$($Emojis.Cross) WSL is not available or not installed" "ERROR" $Colors.Error
        Write-Log "Please install WSL: https://docs.microsoft.com/en-us/windows/wsl/install" "ERROR" $Colors.Error
        return $false
    }
}

function Test-WSLDistro {
    Write-Log "$($Emojis.Info) Checking WSL distribution: $($CONFIG.WSLDistro)" "INFO" $Colors.Info
    
    try {
        $distros = wsl --list --quiet
        if ($distros -contains $CONFIG.WSLDistro) {
            Write-Log "$($Emojis.Check) WSL distribution '$($CONFIG.WSLDistro)' is available" "SUCCESS" $Colors.Success
            return $true
        }
        else {
            Write-Log "$($Emojis.Cross) WSL distribution '$($CONFIG.WSLDistro)' not found" "ERROR" $Colors.Error
            Write-Log "Available distributions: $($distros -join ', ')" "INFO" $Colors.Info
            return $false
        }
    }
    catch {
        Write-Log "$($Emojis.Cross) Failed to check WSL distributions: $($_.Exception.Message)" "ERROR" $Colors.Error
        return $false
    }
}

function Invoke-WSLCommand {
    param(
        [string]$Command,
        [switch]$IgnoreErrors
    )
    
    if ($Verbose) {
        Write-Log "$($Emojis.Gear) Executing WSL command: $Command" "DEBUG" $Colors.Debug
    }
    
    try {
        $result = wsl -d $CONFIG.WSLDistro bash -c $Command 2>&1
        if ($LASTEXITCODE -ne 0 -and -not $IgnoreErrors) {
            throw "Command failed with exit code $LASTEXITCODE`: $result"
        }
        return $result
    }
    catch {
        Write-Log "$($Emojis.Cross) WSL command failed: $($_.Exception.Message)" "ERROR" $Colors.Error
        if (-not $IgnoreErrors) {
            throw
        }
        return $null
    }
}

function Test-NodeJS {
    Write-Log "$($Emojis.Info) Checking Node.js installation in WSL..." "INFO" $Colors.Info
    
    try {
        $nodeVersion = Invoke-WSLCommand "node --version" -IgnoreErrors
        if ($nodeVersion) {
            $version = $nodeVersion.Trim().TrimStart('v')
            $majorVersion = [int]($version.Split('.')[0])
            
            if ($majorVersion -ge $CONFIG.NodeVersion) {
                Write-Log "$($Emojis.Check) Node.js $nodeVersion detected" "SUCCESS" $Colors.Success
                return $true
            }
            else {
                Write-Log "$($Emojis.Warning) Node.js $nodeVersion is too old (required: $($CONFIG.NodeVersion)+)" "WARNING" $Colors.Warning
                return $false
            }
        }
        else {
            Write-Log "$($Emojis.Cross) Node.js not found in WSL" "ERROR" $Colors.Error
            return $false
        }
    }
    catch {
        Write-Log "$($Emojis.Cross) Failed to check Node.js: $($_.Exception.Message)" "ERROR" $Colors.Error
        return $false
    }
}

function Install-NodeJS {
    Write-Log "$($Emojis.Gear) Installing Node.js $($CONFIG.NodeVersion) in WSL..." "INFO" $Colors.Info

    try {
        # Update package list
        Invoke-WSLCommand "sudo apt update"

        # Install Node.js using NodeSource repository
        Invoke-WSLCommand "curl -fsSL https://deb.nodesource.com/setup_$($CONFIG.NodeVersion).x | sudo -E bash -"
        Invoke-WSLCommand "sudo apt-get install -y nodejs"

        # Verify installation
        $nodeVersion = Invoke-WSLCommand "node --version"
        $npmVersion = Invoke-WSLCommand "npm --version"

        Write-Log "$($Emojis.Check) Node.js $nodeVersion installed successfully" "SUCCESS" $Colors.Success
        Write-Log "$($Emojis.Check) npm $npmVersion installed successfully" "SUCCESS" $Colors.Success

        return $true
    }
    catch {
        Write-Log "$($Emojis.Cross) Failed to install Node.js: $($_.Exception.Message)" "ERROR" $Colors.Error
        return $false
    }
}

function Get-ExcludeFilter {
    $excludeArgs = @()
    foreach ($pattern in $CONFIG.ExcludePatterns) {
        $excludeArgs += "--exclude=$pattern"
    }
    return $excludeArgs -join " "
}

function Sync-ProjectFiles {
    param(
        [switch]$InitialSync,
        [string[]]$SpecificFiles = @()
    )

    if ($InitialSync) {
        Write-Log "$($Emojis.Sync) Performing initial project sync..." "INFO" $Colors.Info
    }
    else {
        Write-Log "$($Emojis.Sync) Syncing changed files..." "INFO" $Colors.Info
    }

    try {
        # Create WSL project directory if it doesn't exist
        Invoke-WSLCommand "mkdir -p '$($CONFIG.WSLProjectPath)'"

        # Convert Windows path to WSL path for rsync
        $windowsPath = $CONFIG.WindowsProjectPath.Replace('\', '/')
        $wslWindowsPath = "/mnt/" + $windowsPath.Substring(0, 1).ToLower() + $windowsPath.Substring(2)

        if ($SpecificFiles.Count -gt 0) {
            # Sync specific files
            foreach ($file in $SpecificFiles) {
                $relativePath = [System.IO.Path]::GetRelativePath($CONFIG.WindowsProjectPath, $file)
                $sourceFile = "$wslWindowsPath/$($relativePath.Replace('\', '/'))"
                $targetDir = Split-Path "$($CONFIG.WSLProjectPath)/$($relativePath.Replace('\', '/'))" -Parent

                Invoke-WSLCommand "mkdir -p '$targetDir'"
                Invoke-WSLCommand "cp '$sourceFile' '$($CONFIG.WSLProjectPath)/$($relativePath.Replace('\', '/'))'"

                if ($Verbose) {
                    Write-Log "$($Emojis.Check) Synced: $relativePath" "DEBUG" $Colors.Debug
                }
            }
        }
        else {
            # Full sync using rsync
            $excludeFilter = Get-ExcludeFilter
            $rsyncCommand = "rsync -av $excludeFilter '$wslWindowsPath/' '$($CONFIG.WSLProjectPath)/'"

            Invoke-WSLCommand $rsyncCommand
        }

        Write-Log "$($Emojis.Check) Project files synced successfully" "SUCCESS" $Colors.Success
        return $true
    }
    catch {
        Write-Log "$($Emojis.Cross) Failed to sync project files: $($_.Exception.Message)" "ERROR" $Colors.Error
        return $false
    }
}

function Install-Dependencies {
    Write-Log "$($Emojis.Gear) Installing project dependencies..." "INFO" $Colors.Info

    try {
        # Change to project directory and install dependencies
        $installCommand = "cd '$($CONFIG.WSLProjectPath)' && npm install"
        Invoke-WSLCommand $installCommand

        Write-Log "$($Emojis.Check) Dependencies installed successfully" "SUCCESS" $Colors.Success
        return $true
    }
    catch {
        Write-Log "$($Emojis.Cross) Failed to install dependencies: $($_.Exception.Message)" "ERROR" $Colors.Error
        return $false
    }
}

function Build-Project {
    Write-Log "$($Emojis.Build) Building project..." "INFO" $Colors.Info

    try {
        # Run build commands
        $commands = @(
            "cd '$($CONFIG.WSLProjectPath)' && npm run lint",
            "cd '$($CONFIG.WSLProjectPath)' && npm run format",
            "cd '$($CONFIG.WSLProjectPath)' && npm run build"
        )

        foreach ($command in $commands) {
            Invoke-WSLCommand $command
        }

        Write-Log "$($Emojis.Check) Project built successfully" "SUCCESS" $Colors.Success
        return $true
    }
    catch {
        Write-Log "$($Emojis.Cross) Failed to build project: $($_.Exception.Message)" "ERROR" $Colors.Error
        return $false
    }
}

function Test-Project {
    Write-Log "$($Emojis.Gear) Running project tests..." "INFO" $Colors.Info

    try {
        $testCommand = "cd '$($CONFIG.WSLProjectPath)' && npm test"
        $result = Invoke-WSLCommand $testCommand -IgnoreErrors

        if ($LASTEXITCODE -eq 0) {
            Write-Log "$($Emojis.Check) All tests passed" "SUCCESS" $Colors.Success
            return $true
        }
        else {
            Write-Log "$($Emojis.Warning) Some tests failed, but continuing..." "WARNING" $Colors.Warning
            return $false
        }
    }
    catch {
        Write-Log "$($Emojis.Warning) Failed to run tests: $($_.Exception.Message)" "WARNING" $Colors.Warning
        return $false
    }
}

function Install-GlobalCLI {
    Write-Log "$($Emojis.Gear) Installing CLI globally in WSL..." "INFO" $Colors.Info

    try {
        $installCommand = "cd '$($CONFIG.WSLProjectPath)' && npm install -g ."
        Invoke-WSLCommand $installCommand

        # Test global installation
        $versionCheck = Invoke-WSLCommand "kritrima --version" -IgnoreErrors
        if ($versionCheck) {
            Write-Log "$($Emojis.Check) CLI installed globally: $versionCheck" "SUCCESS" $Colors.Success
            return $true
        }
        else {
            Write-Log "$($Emojis.Warning) Global installation may have failed" "WARNING" $Colors.Warning
            return $false
        }
    }
    catch {
        Write-Log "$($Emojis.Cross) Failed to install CLI globally: $($_.Exception.Message)" "ERROR" $Colors.Error
        return $false
    }
}

function Get-FileHash {
    param([string]$FilePath)

    try {
        $hash = Get-FileHash -Path $FilePath -Algorithm MD5
        return $hash.Hash
    }
    catch {
        return $null
    }
}

function Get-ProjectFiles {
    $files = @()
    foreach ($pattern in $CONFIG.WatchPatterns) {
        $matchedFiles = Get-ChildItem -Path $CONFIG.WindowsProjectPath -Filter $pattern -Recurse -File
        $files += $matchedFiles
    }

    # Filter out excluded patterns
    $filteredFiles = @()
    foreach ($file in $files) {
        $shouldExclude = $false
        foreach ($excludePattern in $CONFIG.ExcludePatterns) {
            if ($file.FullName -like "*$excludePattern*") {
                $shouldExclude = $true
                break
            }
        }
        if (-not $shouldExclude) {
            $filteredFiles += $file
        }
    }

    return $filteredFiles
}

function Start-FileWatcher {
    Write-Log "$($Emojis.Watch) Starting file watcher..." "INFO" $Colors.Info
    Write-Log "Watching for changes in: $($CONFIG.WindowsProjectPath)" "INFO" $Colors.Info
    Write-Log "Press Ctrl+C to stop watching" "INFO" $Colors.Warning

    # Initialize file hash cache
    $fileHashes = @{}
    $projectFiles = Get-ProjectFiles

    foreach ($file in $projectFiles) {
        $fileHashes[$file.FullName] = Get-FileHash -FilePath $file.FullName
    }

    Write-Log "$($Emojis.Check) Monitoring $($projectFiles.Count) files" "INFO" $Colors.Info

    try {
        while ($true) {
            Start-Sleep -Seconds 2

            # Check for changes
            $changedFiles = @()
            $currentFiles = Get-ProjectFiles

            # Check existing files for changes
            foreach ($file in $currentFiles) {
                $currentHash = Get-FileHash -FilePath $file.FullName
                $previousHash = $fileHashes[$file.FullName]

                if ($currentHash -ne $previousHash) {
                    $changedFiles += $file.FullName
                    $fileHashes[$file.FullName] = $currentHash
                }
            }

            # Check for new files
            foreach ($file in $currentFiles) {
                if (-not $fileHashes.ContainsKey($file.FullName)) {
                    $changedFiles += $file.FullName
                    $fileHashes[$file.FullName] = Get-FileHash -FilePath $file.FullName
                }
            }

            # Check for deleted files
            $currentFilePaths = $currentFiles | ForEach-Object { $_.FullName }
            $deletedFiles = @()
            foreach ($cachedFile in $fileHashes.Keys) {
                if ($cachedFile -notin $currentFilePaths) {
                    $deletedFiles += $cachedFile
                }
            }

            # Remove deleted files from cache
            foreach ($deletedFile in $deletedFiles) {
                $fileHashes.Remove($deletedFile)
                Write-Log "$($Emojis.Warning) File deleted: $(Split-Path $deletedFile -Leaf)" "WARNING" $Colors.Warning
            }

            # Process changed files
            if ($changedFiles.Count -gt 0) {
                Write-Log "$($Emojis.Sync) Detected $($changedFiles.Count) changed file(s)" "INFO" $Colors.Info

                foreach ($file in $changedFiles) {
                    $fileName = Split-Path $file -Leaf
                    Write-Log "$($Emojis.Info) Changed: $fileName" "INFO" $Colors.Info
                }

                # Sync changed files
                if (Sync-ProjectFiles -SpecificFiles $changedFiles) {
                    # Only rebuild if sync was successful
                    if ($changedFiles | Where-Object { $_ -like "*.js" -or $_ -like "*.json" }) {
                        Write-Log "$($Emojis.Build) Rebuilding project due to code changes..." "INFO" $Colors.Info
                        Build-Project | Out-Null
                    }
                }
            }
        }
    }
    catch [System.Management.Automation.PipelineStoppedException] {
        Write-Log "$($Emojis.Info) File watching stopped by user" "INFO" $Colors.Info
    }
    catch {
        Write-Log "$($Emojis.Cross) File watcher error: $($_.Exception.Message)" "ERROR" $Colors.Error
    }
}

function Invoke-InitialSetup {
    Write-Log "$($Emojis.Rocket) Starting initial setup..." "INFO" $Colors.Info

    # Check prerequisites
    if (-not (Test-WSLAvailability)) { return $false }
    if (-not (Test-WSLDistro)) { return $false }

    # Install Node.js if needed
    if (-not (Test-NodeJS)) {
        if (-not (Install-NodeJS)) { return $false }
    }

    # Sync project files
    if (-not (Sync-ProjectFiles -InitialSync)) { return $false }

    # Install dependencies
    if (-not (Install-Dependencies)) { return $false }

    # Build project
    if (-not (Build-Project)) { return $false }

    # Run tests (optional)
    Test-Project | Out-Null

    # Install globally (optional)
    $installGlobal = Read-Host "Install Kritrima CLI globally in WSL? (y/N)"
    if ($installGlobal -eq 'y' -or $installGlobal -eq 'Y') {
        Install-GlobalCLI | Out-Null
    }

    Write-Log "$($Emojis.Check) Initial setup completed successfully!" "SUCCESS" $Colors.Success
    return $true
}

function Invoke-BuildOnly {
    Write-Log "$($Emojis.Build) Running build-only mode..." "INFO" $Colors.Info

    # Check prerequisites
    if (-not (Test-WSLAvailability)) { return $false }
    if (-not (Test-WSLDistro)) { return $false }

    # Check if project exists in WSL
    $projectExists = Invoke-WSLCommand "test -d '$($CONFIG.WSLProjectPath)'" -IgnoreErrors
    if ($LASTEXITCODE -ne 0) {
        Write-Log "$($Emojis.Cross) Project not found in WSL. Run with -InitialSetup first." "ERROR" $Colors.Error
        return $false
    }

    # Build project
    if (-not (Build-Project)) { return $false }

    # Run tests
    Test-Project | Out-Null

    Write-Log "$($Emojis.Check) Build completed successfully!" "SUCCESS" $Colors.Success
    return $true
}

function Show-Summary {
    Write-Host ""
    Write-Host "$($Emojis.Check) Setup Summary" -ForegroundColor $Colors.Header
    Write-Host "==================" -ForegroundColor $Colors.Header
    Write-Host ""
    Write-Host "Windows Project Path: $($CONFIG.WindowsProjectPath)" -ForegroundColor $Colors.Info
    Write-Host "WSL Project Path:     $($CONFIG.WSLProjectPath)" -ForegroundColor $Colors.Info
    Write-Host "WSL Distribution:     $($CONFIG.WSLDistro)" -ForegroundColor $Colors.Info
    Write-Host ""
    Write-Host "Quick Commands:" -ForegroundColor $Colors.Info
    Write-Host "  # Test CLI in WSL" -ForegroundColor White
    Write-Host "  wsl -d $($CONFIG.WSLDistro) -- bash -c 'cd $($CONFIG.WSLProjectPath) && node src/index.js --version'" -ForegroundColor Gray
    Write-Host ""
    Write-Host "  # Start interactive session" -ForegroundColor White
    Write-Host "  wsl -d $($CONFIG.WSLDistro) -- bash -c 'cd $($CONFIG.WSLProjectPath) && node src/index.js'" -ForegroundColor Gray
    Write-Host ""
    Write-Host "  # Watch for changes" -ForegroundColor White
    Write-Host "  .\sync-to-wsl.ps1 -WatchMode" -ForegroundColor Gray
    Write-Host ""
}

# ============================================================================
# MAIN EXECUTION
# ============================================================================

# Handle Ctrl+C gracefully
$null = Register-EngineEvent PowerShell.Exiting -Action {
    Write-Host ""
    Write-Host "$($Emojis.Info) Script interrupted by user" -ForegroundColor $Colors.Info
}

# Show help if requested
if ($Help) {
    Show-Help
    exit 0
}

# Show header
Show-Header

# Validate parameters
if (-not (Test-Path $CONFIG.WindowsProjectPath)) {
    Write-Log "$($Emojis.Cross) Windows project path not found: $($CONFIG.WindowsProjectPath)" "ERROR" $Colors.Error
    exit 1
}

# Update configuration with resolved paths
$CONFIG.WindowsProjectPath = Resolve-Path $CONFIG.WindowsProjectPath
$CONFIG.WSLProjectPath = $CONFIG.WSLProjectPath.Replace('$env:USERNAME', $env:USERNAME)

Write-Log "$($Emojis.Info) Windows Project: $($CONFIG.WindowsProjectPath)" "INFO" $Colors.Info
Write-Log "$($Emojis.Info) WSL Project: $($CONFIG.WSLProjectPath)" "INFO" $Colors.Info
Write-Log "$($Emojis.Info) WSL Distribution: $($CONFIG.WSLDistro)" "INFO" $Colors.Info

# Initialize log file
if (-not (Test-Path $CONFIG.LogFile)) {
    New-Item -Path $CONFIG.LogFile -ItemType File -Force | Out-Null
}

Write-Log "$($Emojis.Rocket) Kritrima AI CLI - WSL Auto-Sync Started" "INFO" $Colors.Info

try {
    # Execute based on mode
    if ($InitialSetup) {
        Write-Log "$($Emojis.Gear) Running initial setup mode..." "INFO" $Colors.Info

        if (Invoke-InitialSetup) {
            Show-Summary

            # Ask if user wants to start watching
            $startWatch = Read-Host "Start file watching mode now? (Y/n)"
            if ($startWatch -ne 'n' -and $startWatch -ne 'N') {
                Write-Host ""
                Start-FileWatcher
            }
        }
        else {
            Write-Log "$($Emojis.Cross) Initial setup failed" "ERROR" $Colors.Error
            exit 1
        }
    }
    elseif ($WatchMode) {
        Write-Log "$($Emojis.Watch) Running watch mode..." "INFO" $Colors.Info

        # Check if project exists in WSL
        if (-not (Test-WSLAvailability) -or -not (Test-WSLDistro)) {
            exit 1
        }

        $projectExists = Invoke-WSLCommand "test -d '$($CONFIG.WSLProjectPath)'" -IgnoreErrors
        if ($LASTEXITCODE -ne 0) {
            Write-Log "$($Emojis.Cross) Project not found in WSL. Run with -InitialSetup first." "ERROR" $Colors.Error
            exit 1
        }

        Start-FileWatcher
    }
    elseif ($BuildOnly) {
        Write-Log "$($Emojis.Build) Running build-only mode..." "INFO" $Colors.Info

        if (-not (Invoke-BuildOnly)) {
            exit 1
        }
    }
    else {
        # Default: Show help and ask what to do
        Write-Host "What would you like to do?" -ForegroundColor $Colors.Info
        Write-Host ""
        Write-Host "1. Initial Setup (sync files, install dependencies, build)" -ForegroundColor White
        Write-Host "2. Watch Mode (monitor for changes and auto-sync)" -ForegroundColor White
        Write-Host "3. Build Only (run build commands)" -ForegroundColor White
        Write-Host "4. Show Help" -ForegroundColor White
        Write-Host ""

        $choice = Read-Host "Enter your choice (1-4)"

        switch ($choice) {
            "1" {
                if (Invoke-InitialSetup) {
                    Show-Summary
                }
            }
            "2" {
                # Check if project exists first
                if (-not (Test-WSLAvailability) -or -not (Test-WSLDistro)) {
                    exit 1
                }

                $projectExists = Invoke-WSLCommand "test -d '$($CONFIG.WSLProjectPath)'" -IgnoreErrors
                if ($LASTEXITCODE -ne 0) {
                    Write-Log "$($Emojis.Cross) Project not found in WSL. Please run Initial Setup first." "ERROR" $Colors.Error
                    exit 1
                }

                Start-FileWatcher
            }
            "3" {
                Invoke-BuildOnly | Out-Null
            }
            "4" {
                Show-Help
            }
            default {
                Write-Log "$($Emojis.Cross) Invalid choice. Use -Help for usage information." "ERROR" $Colors.Error
                exit 1
            }
        }
    }
}
catch {
    Write-Log "$($Emojis.Cross) Unexpected error: $($_.Exception.Message)" "ERROR" $Colors.Error
    Write-Log "Stack trace: $($_.ScriptStackTrace)" "ERROR" $Colors.Error
    exit 1
}
finally {
    Write-Log "$($Emojis.Info) Script execution completed" "INFO" $Colors.Info
}

Write-Host ""
Write-Host "$($Emojis.Check) Thank you for using Kritrima AI CLI WSL Auto-Sync!" -ForegroundColor $Colors.Success
Write-Host ""
