# Kritrima AI CLI - WSL Auto-Sync Configuration Template
# Copy this file to sync-config.ps1 and customize as needed

# ============================================================================
# BASIC CONFIGURATION
# ============================================================================

# WSL Distribution to use (run 'wsl --list' to see available distributions)
$WSLDistro = "Ubuntu"

# Project path in WSL (will be created if it doesn't exist)
$WSLProjectPath = "/home/<USER>/kritrima-ai-cli"

# Windows project path (usually current directory)
$WindowsProjectPath = $PSScriptRoot

# Required Node.js major version
$NodeVersion = "18"

# ============================================================================
# FILE PATTERNS
# ============================================================================

# Files to exclude from sync (patterns)
$ExcludePatterns = @(
    "node_modules",
    ".git",
    "*.log",
    ".env",
    "sync-log.txt",
    "*.tmp",
    ".DS_Store",
    "Thumbs.db",
    "*.cache",
    "coverage",
    "dist",
    "build"
)

# Files to watch for changes (patterns)
$WatchPatterns = @(
    "*.js",
    "*.json",
    "*.md",
    "*.yml",
    "*.yaml",
    "*.sh",
    "*.ps1",
    "*.ts",
    "*.tsx",
    "*.jsx"
)

# ============================================================================
# ADVANCED SETTINGS
# ============================================================================

# Enable verbose logging by default
$VerboseLogging = $false

# Auto-install CLI globally after initial setup
$AutoInstallGlobal = $true

# Auto-start watch mode after initial setup
$AutoStartWatch = $true

# Build commands to run (in order)
$BuildCommands = @(
    "npm run lint",
    "npm run format", 
    "npm run build"
)

# Test command (optional)
$TestCommand = "npm test"

# Run tests automatically after build
$AutoRunTests = $true

# ============================================================================
# NOTIFICATION SETTINGS
# ============================================================================

# Show Windows notifications (requires Windows 10/11)
$ShowNotifications = $true

# Play sound on completion
$PlaySounds = $false

# ============================================================================
# PERFORMANCE SETTINGS
# ============================================================================

# File watching interval in seconds
$WatchInterval = 2

# Maximum file size to sync (in bytes, 0 = no limit)
$MaxFileSize = 10485760  # 10MB

# Use rsync for better performance (requires rsync in WSL)
$UseRsync = $true

# ============================================================================
# BACKUP SETTINGS
# ============================================================================

# Create backup before overwriting files
$CreateBackups = $true

# Backup directory in WSL
$BackupDirectory = "/tmp/kritrima-backups"

# Maximum number of backups to keep
$MaxBackups = 5

# ============================================================================
# CUSTOM HOOKS
# ============================================================================

# Custom script to run before sync (optional)
$PreSyncScript = ""

# Custom script to run after sync (optional)
$PostSyncScript = ""

# Custom script to run after build (optional)
$PostBuildScript = ""

# ============================================================================
# EXAMPLE CUSTOM CONFIGURATIONS
# ============================================================================

<#
# Example: Different configuration for different environments

if ($env:COMPUTERNAME -eq "DEV-MACHINE") {
    $WSLDistro = "Ubuntu-22.04"
    $WSLProjectPath = "/opt/kritrima-dev"
    $VerboseLogging = $true
}

# Example: Custom build commands for different project types
if (Test-Path "package.json") {
    $projectType = (Get-Content "package.json" | ConvertFrom-Json).scripts
    if ($projectType.PSObject.Properties.Name -contains "build:prod") {
        $BuildCommands += "npm run build:prod"
    }
}

# Example: Custom file patterns based on project structure
if (Test-Path "src/components") {
    $WatchPatterns += "*.vue"
    $WatchPatterns += "*.scss"
}

# Example: Environment-specific exclusions
if ($env:NODE_ENV -eq "development") {
    $ExcludePatterns += "*.min.js"
    $ExcludePatterns += "*.min.css"
}
#>

# ============================================================================
# VALIDATION
# ============================================================================

# Validate configuration
if (-not $WSLDistro) {
    Write-Error "WSLDistro must be specified"
    exit 1
}

if (-not $WSLProjectPath) {
    Write-Error "WSLProjectPath must be specified"
    exit 1
}

if ($NodeVersion -lt 14) {
    Write-Warning "Node.js version $NodeVersion may not be supported. Recommended: 18+"
}

# Export configuration for use by main script
$Global:SyncConfig = @{
    WSLDistro = $WSLDistro
    WSLProjectPath = $WSLProjectPath
    WindowsProjectPath = $WindowsProjectPath
    NodeVersion = $NodeVersion
    ExcludePatterns = $ExcludePatterns
    WatchPatterns = $WatchPatterns
    VerboseLogging = $VerboseLogging
    AutoInstallGlobal = $AutoInstallGlobal
    AutoStartWatch = $AutoStartWatch
    BuildCommands = $BuildCommands
    TestCommand = $TestCommand
    AutoRunTests = $AutoRunTests
    ShowNotifications = $ShowNotifications
    PlaySounds = $PlaySounds
    WatchInterval = $WatchInterval
    MaxFileSize = $MaxFileSize
    UseRsync = $UseRsync
    CreateBackups = $CreateBackups
    BackupDirectory = $BackupDirectory
    MaxBackups = $MaxBackups
    PreSyncScript = $PreSyncScript
    PostSyncScript = $PostSyncScript
    PostBuildScript = $PostBuildScript
}

Write-Host "✅ Configuration loaded successfully" -ForegroundColor Green
