@echo off
REM Kritrima AI CLI - WSL Auto-Sync Batch Launcher
REM Simple batch file to launch the PowerShell script

setlocal

REM Check if PowerShell is available
where powershell >nul 2>nul
if %errorlevel% neq 0 (
    echo Error: PowerShell not found. Please install PowerShell.
    pause
    exit /b 1
)

REM Get the directory where this batch file is located
set "SCRIPT_DIR=%~dp0"

REM Check if the PowerShell script exists
if not exist "%SCRIPT_DIR%sync-to-wsl.ps1" (
    echo Error: sync-to-wsl.ps1 not found in %SCRIPT_DIR%
    pause
    exit /b 1
)

REM Display menu
echo.
echo 🤖 Kritrima AI CLI - WSL Auto-Sync
echo ===================================
echo.
echo What would you like to do?
echo.
echo 1. Initial Setup (first time setup)
echo 2. Watch Mode (monitor for changes)
echo 3. Build Only (run build commands)
echo 4. Help (show detailed help)
echo.

set /p choice="Enter your choice (1-4): "

REM Execute based on choice
if "%choice%"=="1" (
    echo.
    echo Starting Initial Setup...
    powershell -ExecutionPolicy Bypass -File "%SCRIPT_DIR%sync-to-wsl.ps1" -InitialSetup
) else if "%choice%"=="2" (
    echo.
    echo Starting Watch Mode...
    powershell -ExecutionPolicy Bypass -File "%SCRIPT_DIR%sync-to-wsl.ps1" -WatchMode
) else if "%choice%"=="3" (
    echo.
    echo Running Build Only...
    powershell -ExecutionPolicy Bypass -File "%SCRIPT_DIR%sync-to-wsl.ps1" -BuildOnly
) else if "%choice%"=="4" (
    echo.
    echo Showing Help...
    powershell -ExecutionPolicy Bypass -File "%SCRIPT_DIR%sync-to-wsl.ps1" -Help
) else (
    echo.
    echo Invalid choice. Please run the script again.
)

echo.
pause
